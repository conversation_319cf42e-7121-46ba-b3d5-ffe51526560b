'use client'

import { useState } from 'react'
import { CollapsibleSection } from '../component/collapsible'
import { PackageDetailsForm } from '../component/package-detail-form'
import { SeoDetailsSection } from '@/components/package/seo-detail-section'
import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section'
import { PackageFormData } from '@/types/package'
import { Button } from '@/components/ui/button'


interface PackageCreatePageProps {
    onSave?: (data: PackageFormData) => void
    onCancel?: () => void
}

const getInitialPackageData = (): PackageFormData => ({
    name: '',
    slug: '',
    regionId: '',
    activityId: '',
    accomodation: '',
    distance: '',
    type: '',
    duration: '',
    altitude: '',
    meals: '',
    groupSize: '',
    price: '',
    discountPrice: '',
    bestSeason: '',
    transport: '',
    activityPerDay: '',
    grade: '',
    bookingLink: '',
    overviewDescription: '',
    thumbnail: '',
    mainImage: '',
    mainImageAlt: '',
    pdfBrochure: '',
    published: false,
    tripOftheMonth: false,
    popularTour: false,
    shortTrek: false,
})

export default function PackageCreatePage({ onSave, onCancel }: PackageCreatePageProps) {
    const [packageData, setPackageData] = useState<PackageFormData>(getInitialPackageData())
    const [seoOpen, setSeoOpen] = useState(false)
    const [schemaOpen, setSchemaOpen] = useState(false)
    const [packageEditOpen, setPackageEditOpen] = useState(true)

    const handleSave = () => {
        if (onSave) {
            onSave(packageData)
        } else {
            console.log('Saving package data:', packageData)
            // TODO: Implement actual save logic here
        }
    }


    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="container mx-auto space-y-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-3xl font-bold text-gray-900">Create New Package</h1>

                </div>

                <CollapsibleSection
                    title="SEO Details"
                    isOpen={seoOpen}
                    onToggle={() => setSeoOpen(!seoOpen)}
                >
                    <SeoDetailsSection
                        formData={{
                            metaTitle: packageData.name,
                            metaDescription: packageData.overviewDescription,
                            metaKeywords: '',
                            canonicalUrl: ''
                        }}
                        setFormData={(seoFields) => {
                            setPackageData(prev => ({ ...prev, ...seoFields }))
                        }}
                    />
                </CollapsibleSection>

                <CollapsibleSection
                    title="Schema Details"
                    isOpen={schemaOpen}
                    onToggle={() => setSchemaOpen(!schemaOpen)}
                >
                    <SchemaDetailsSection
                        schema={packageData.slug}
                        setSchema={(schema) => {
                            setPackageData(prev => ({ ...prev, schema }))
                        }}
                    />
                </CollapsibleSection>

                <CollapsibleSection
                    title="Package Details"
                    isOpen={packageEditOpen}
                    onToggle={() => setPackageEditOpen(!packageEditOpen)}
                >
                    <PackageDetailsForm
                        formData={packageData}
                        onFormDataChange={(updatedData) => setPackageData(updatedData)}
                    />
                </CollapsibleSection>

                <div className="flex justify-end gap-2">

                    <Button
                        onClick={onCancel}
                        variant="outline"
                        className="px-4 py-2  text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Cancel
                    </Button>

                    <Button
                        onClick={handleSave}
                        className="px-4 py-2 bg-brand text-white rounded-md hover:bg-brand/80"
                    >
                        Create Package
                    </Button>
                </div>
            </div>
        </div>
    )
}
