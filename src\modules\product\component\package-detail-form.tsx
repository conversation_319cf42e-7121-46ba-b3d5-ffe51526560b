'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { FileUpload } from "./fileupload"
import dynamic from "next/dynamic"
import { PackageFormData } from "@/types/package"
import { Checkbox } from "@/components/ui/checkbox"

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface PackageDetailsFormProps {
  formData: PackageFormData
  onFormDataChange: (data: PackageFormData) => void
}

export function PackageDetailsForm({ formData, onFormDataChange }: PackageDetailsFormProps) {

  const handleInputChange = (field: keyof PackageFormData, value: string | number | boolean) => {
    onFormDataChange({
      ...formData,
      [field]: value
    })
  }

  const handleFileChange = (field: 'mainImage' | 'thumbnail' | 'pdfFile', file: File | null) => {
    onFormDataChange({
      ...formData,
      [field]: file || undefined
    })
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="package-name" className="mb-2">Package Name(H1)</Label>
              <Input
                id="package-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="Enter package name"
              />
            </div>

            <div>
              <Label htmlFor="region" className="mb-2">Select Region</Label>
              <Select
                value={formData.regionId}
                onValueChange={(value) => handleInputChange('regionId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="annapurna">Annapurna Region</SelectItem>
                  <SelectItem value="everest">Everest Region</SelectItem>
                  <SelectItem value="langtang">Langtang Region</SelectItem>
                  <SelectItem value="manaslu">Manaslu Region</SelectItem>
                  <SelectItem value="mustang">Mustang Region</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="accommodation" className="mb-2">Accommodation</Label>
              <Input
                id="accommodation"
                value={formData.accomodation}
                onChange={(e) => handleInputChange('accomodation', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., Hotel, Lodge and Tea house"
              />
            </div>

            <div>
              <Label htmlFor="trail-type" className="mb-2">Trail Type</Label>
              <Input
                id="trail-type"
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., Mountain trail, Forest path"
              />
            </div>

            <div>
              <Label htmlFor="max-altitude" className="mb-2">Max Altitude</Label>
              <Input
                id="max-altitude"
                value={formData.altitude}
                onChange={(e) => handleInputChange('altitude', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 4,600 m | 15091 ft"
              />
            </div>

            <div>
              <Label htmlFor="group-size" className="mb-2">Group Size</Label>
              <Input
                id="group-size"
                value={formData.groupSize}
                onChange={(e) => handleInputChange('groupSize', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 1-25"
              />
            </div>

            <div>
              <Label htmlFor="best-season" className="mb-2">Best Season</Label>
              <Input
                id="best-season"
                value={formData.bestSeason}
                onChange={(e) => handleInputChange('bestSeason', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., March-May / August-November"
              />
            </div>

            <div>
              <Label htmlFor="price" className="mb-2">Price</Label>
              <Input
                id="price"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., $ 630"
              />
            </div>

            <div>
              <FileUpload
                label="Main Image"
                accept="image/*"
                showPreview={true}
                previewAlt="Package preview"
                onFileChange={(file) => handleFileChange('mainImage', file)}
              />
            </div>

            <div>
              <Label htmlFor="image-alt" className="mb-2">Image Alt Tag</Label>
              <Input
                id="image-alt"
                value={formData.mainImageAlt}
                onChange={(e) => handleInputChange('mainImageAlt', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="Descriptive alt text for the main image"
              />
            </div>

            <div>
              <FileUpload
                label="PDF Brochure"
                accept=".pdf"
                onFileChange={(file) => handleFileChange('pdfFile', file)}
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4" >
            <div>
              <Label htmlFor="activity" className="mb-2">Select Activity</Label>
              <Select
                value={formData.activityId}
                onValueChange={(value) => handleInputChange('activityId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an activity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="trekking">Trekking</SelectItem>
                  <SelectItem value="peak-climbing">Peak Climbing</SelectItem>
                  <SelectItem value="trail-running">Trail Running</SelectItem>
                  <SelectItem value="fastpacking">Fast Packing</SelectItem>
                  <SelectItem value="expedition">Expedition</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="slug" className="mb-2">Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => handleInputChange('slug', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., khopra-trek-nepal"
              />
            </div>

            <div>
              <Label htmlFor="distance" className="mb-2">Distance</Label>
              <Input
                id="distance"
                value={formData.distance}
                onChange={(e) => handleInputChange('distance', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 55 km"
              />
            </div>

            {/* <div>
              <Label htmlFor="days-nights">Days And Nights</Label>
              <Input 
                id="days-nights" 
                value={formData.daysNights}
                onChange={(e) => handleInputChange('daysNights', e.target.value)}
                placeholder="e.g., 6 Nights 7 Days"
              />
            </div> */}

            <div>
              <Label htmlFor="meals" className="mb-2">Meals Include</Label>
              <Input
                id="meals"
                value={formData.meals}
                onChange={(e) => handleInputChange('meals', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., Breakfast, Lunch & Dinner"
              />
            </div>

            <div>
              <Label htmlFor="discount-price" className="mb-2">Discount Price</Label>
              <Input
                id="discount-price"
                value={formData.discountPrice}
                onChange={(e) => handleInputChange('discountPrice', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., $ 525"
              />
            </div>

            <div>
              <Label htmlFor="transportation" className="mb-2">Transportation</Label>
              <Input
                id="transportation"
                value={formData.transport}
                onChange={(e) => handleInputChange('transport', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., Jeep / bus"
              />
            </div>

            <div>
              <Label htmlFor="booking-link" className="mb-2">Booking Link</Label>
              <Input
                id="booking-link"
                value={formData.bookingLink}
                onChange={(e) => handleInputChange('bookingLink', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="WeTravel booking widget code or link"
              />
            </div>

            <div>
              <FileUpload
                label="Thumbnail"
                accept="image/*"
                showPreview={true}
                previewAlt="Thumbnail preview"
                onFileChange={(file) => handleFileChange('thumbnail', file)}
              />
            </div>

            <div>
              <Label htmlFor="activity-per-day" className="mb-2">Activity Per Day</Label>
              <Input
                id="activity-per-day"
                value={formData.activityPerDay}
                onChange={(e) => handleInputChange('activityPerDay', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 6-7 hrs"
              />
            </div>

            <div>
              <Label htmlFor="grade" className="mb-2">Grade</Label>
              <Input
                id="grade"
                value={formData.grade}
                onChange={(e) => handleInputChange('grade', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., Moderate, Easy, Challenging"
              />
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Label htmlFor="overview" className="mb-2">Overview Description</Label>
          <div className="mt-1">
            <RichTextEditor
              value={formData.overviewDescription}
              onChange={(data) => handleInputChange('overviewDescription', data)}
            />
          </div>
        </div>

        <div className="flex flex-col space-y-3 col-span-2">
          <div className="flex gap-4 flex-wrap mt-8">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="published"
                checked={formData.published || false}
                onCheckedChange={(checked) => handleInputChange('published', !!checked)}
                className="border-black/20 rounded-none"
              />
              <Label htmlFor="published">Published</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="tripOfTheMonth"
                checked={formData.tripOftheMonth || false}
                onCheckedChange={(checked) => handleInputChange('tripOftheMonth', !!checked)}
                className="border-black/20 rounded-none"
              />
              <Label htmlFor="tripOfTheMonth">Trip of the Month</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="popularTour"
                checked={formData.popularTour || false}
                onCheckedChange={(checked) => handleInputChange('popularTour', !!checked)}
                className="border-black/20 rounded-none"
              />
              <Label htmlFor="popularTour">Popular Tour</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="shortTrek"
                checked={formData.shortTrek || false}
                onCheckedChange={(checked) => handleInputChange('shortTrek', !!checked)}
                className="border-black/20 rounded-none"
              />
              <Label htmlFor="shortTrek">Short Trek</Label>
            </div>
          </div>
        </div>

      </CardContent>
    </Card>
  )
}


// 'use client'

// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// import { Card, CardContent } from "@/components/ui/card"
// import { FileUpload } from "./fileupload"
// import dynamic from "next/dynamic"
// import { useState } from "react"

// const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

// export function PackageDetailsForm() {
//   const [overview, setOverview] = useState("")
//   return (
//     <Card>
//       <CardContent className="pt-6">
//         <div className="grid grid-cols-2 gap-6">
//           {/* Left Column */}
//           <div className="space-y-4">
//             <div>
//               <Label htmlFor="package-name">Package Name(H1)</Label>
//               <Input id="package-name" defaultValue="Khopra Ridge Trek" />
//             </div>
//             <div>
//               <Label htmlFor="region">Select Region</Label>
//               <Select defaultValue="annapurna">
//                 <SelectTrigger>
//                   <SelectValue />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="annapurna">Annapurna Region</SelectItem>
//                   <SelectItem value="everest">Everest Region</SelectItem>
//                   <SelectItem value="langtang">Langtang Region</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div>
//               <Label htmlFor="accommodation">Accommodation</Label>
//               <Input id="accommodation" defaultValue="Hotel, Lodge and Tea house" />
//             </div>
//             <div>
//               <Label htmlFor="trail-type">Trail Type</Label>
//               <Input id="trail-type" defaultValue="" />
//             </div>
//             <div>
//               <Label htmlFor="max-altitude">Max Altitude</Label>
//               <Input id="max-altitude" defaultValue="4,600 m | 15091 ft" />
//             </div>
//             <div>
//               <Label htmlFor="group-size">Group Size</Label>
//               <Input id="group-size" defaultValue="1-25" />
//             </div>
//             <div>
//               <Label htmlFor="best-season">Best Season</Label>
//               <Input id="best-season" defaultValue="March-May / August-November" />
//             </div>
//             <div>
//               <Label htmlFor="price">Price</Label>
//               <Input id="price" defaultValue="$ 630" />
//             </div>
//             <div>
//               <Label htmlFor="activity-per-day">Activity Per Day</Label>
//               <Input id="activity-per-day" defaultValue="6-7 hrs" />
//             </div>
//             <div>
//               <Label htmlFor="grade">Grade</Label>
//               <Input id="grade" defaultValue="Moderate" />
//             </div>
//             <div>
//               <FileUpload
//                 label="Image"
//                 accept="image/*"
//                 showPreview={true}
//                 previewAlt="Package preview" onFileChange={function (): void {
//                   throw new Error("Function not implemented.")
//                 } }              />
//             </div>
//             <div>
//               <FileUpload
//                 label="PDF"
//                 accept=".pdf" onFileChange={function (): void {
//                   throw new Error("Function not implemented.")
//                 } }              />
//             </div>
//           </div>

//           {/* Right Column */}
//           <div className="space-y-4">
//             <div>
//               <Label htmlFor="activity">Select Activity</Label>
//               <Select defaultValue="trekking">
//                 <SelectTrigger>
//                   <SelectValue />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="trekking">Trekking</SelectItem>
//                   <SelectItem value="peak-climbing">Peak Climbing</SelectItem>
//                   <SelectItem value="trail-running">Trail Running</SelectItem>
//                   <SelectItem value="fastpacking">Fast Packing</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div>
//               <Label htmlFor="slug">Slug</Label>
//               <Input id="slug" defaultValue="khopra-trek-nepal" />
//             </div>
//             <div>
//               <Label htmlFor="distance">Distance</Label>
//               <Input id="distance" defaultValue="55 km" />
//             </div>
//             <div>
//               <Label htmlFor="days-nights">Days And Nights (eg. 3 Nights 4 Days)</Label>
//               <Input id="days-nights" defaultValue="6 Nights 7 Days" />
//             </div>
//             <div>
//               <Label htmlFor="meals">Meals Include</Label>
//               <Input id="meals" defaultValue="Breakfast, Lunch & Dinner" />
//             </div>
//             <div>
//               <Label htmlFor="discount-price">Discount Price</Label>
//               <Input id="discount-price" defaultValue="$ 525" />
//             </div>
//             <div>
//               <Label htmlFor="transportation">Transportation</Label>
//               <Input id="transportation" defaultValue="Jeep / bus" />
//             </div>
//             <div>
//               <Label htmlFor="image-alt">Image Alt Tag</Label>
//               <Input id="image-alt" defaultValue="trekkers enjoying the view in Khopra ridge and Mt. Dhaulagiri as seen in background" />
//             </div>
//             <div>
//               <FileUpload
//                 label="Thumbnail"
//                 accept="image/*"
//                 showPreview={true}
//                 previewSrc="/placeholder.svg?height=80&width=120"
//                 previewAlt="Thumbnail preview" onFileChange={function (): void {
//                   throw new Error("Function not implemented.")
//                 } }              />
//             </div>
//             <div>
//               <Label htmlFor="booking-link">Booking link</Label>
//               <Input id="booking-link" defaultValue='<button class="wtrvl-checkout_button" id="wetravel_button_widget" data-env="https://ww' />
//             </div>
//           </div>
//         </div>

//         <div className="mt-6">
//           <Label htmlFor="overview">Overview Description</Label>
//           <div className="mt-1">
//             <RichTextEditor value={overview} onChange={data => setOverview(data)} />

//           </div>
//         </div>
//       </CardContent>
//     </Card>
//   )
// }
